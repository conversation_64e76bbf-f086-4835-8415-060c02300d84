#!/usr/bin/env python3
"""
Test script to verify splash screen works without animations.
"""

import sys
from pathlib import Path

# Add src directory to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PySide6.QtWidgets import QApplication
from gui.splash_screen import VisionLabAiSplashScreen
import time

def test_splash_screen():
    """Test the splash screen without animations."""
    app = QApplication(sys.argv)
    
    print("Creating splash screen...")
    splash = VisionLabAiSplashScreen()
    splash.show()
    
    print("Splash screen created and shown successfully!")
    print("Testing progress updates...")
    
    # Test progress updates
    for i in range(0, 101, 20):
        splash.update_progress(i, f"Loading... {i}%")
        app.processEvents()
        time.sleep(0.5)
    
    print("Progress updates completed successfully!")
    print("Splash screen test passed - no animations detected!")
    
    # Keep splash visible for a moment
    time.sleep(2)
    splash.close()
    
    return True

if __name__ == "__main__":
    try:
        success = test_splash_screen()
        if success:
            print("\n✅ SUCCESS: Splash screen works correctly without animations!")
        else:
            print("\n❌ FAILED: Splash screen test failed")
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        sys.exit(1)